package com.example.medicine.service;

import com.example.medicine.dto.ChangePasswordRequest;
import com.example.medicine.dto.LoginRequest;
import com.example.medicine.dto.LoginResponse;
import com.example.medicine.dto.UserProfileDto;
import com.example.medicine.dto.UserSettingsDto;

public interface AuthService {
    LoginResponse login(LoginRequest loginRequest);
    void logout(String token);
    UserProfileDto getUserProfile(String token);
    UserProfileDto updateUserProfile(String token, UserProfileDto profileDto);
    UserSettingsDto getUserSettings(String token);
    UserSettingsDto updateUserSettings(String token, UserSettingsDto settingsDto);
    void changePassword(String token, ChangePasswordRequest changePasswordRequest);
}
