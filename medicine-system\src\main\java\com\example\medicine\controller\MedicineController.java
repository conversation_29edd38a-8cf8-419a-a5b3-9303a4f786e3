package com.example.medicine.controller;

import com.example.medicine.dto.MedicineSearchParams;
import com.example.medicine.entity.Medicine;
import com.example.medicine.entity.MedicineCategory;
import com.example.medicine.service.MedicineService;
import com.example.medicine.service.MedicineCategoryService;
import com.example.medicine.common.Result;
import com.example.medicine.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/medicine")
public class MedicineController {

    @Autowired
    private MedicineService medicineService;

    @Autowired
    private MedicineCategoryService categoryService;

    @GetMapping("/list")
    public Result<PageResult<Medicine>> list(@RequestParam(defaultValue = "1") int page,
                                           @RequestParam(defaultValue = "10") int size,
                                           @RequestParam(required = false) String keyword,
                                           @RequestParam(required = false) String name,
                                           @RequestParam(required = false) Long categoryId,
                                           @RequestParam(required = false) String spec,
                                           @RequestParam(required = false) String batchNo,
                                           @RequestParam(required = false) Integer status,
                                           @RequestParam(required = false) Date expireDate) {
        try {
            // 构建搜索参数
            MedicineSearchParams params = new MedicineSearchParams();
            params.setPage(page);
            params.setSize(size);
            params.setKeyword(keyword);
            params.setName(name);
            params.setCategoryId(categoryId);
            params.setSpec(spec);
            params.setBatchNo(batchNo);
            params.setStatus(status);
            params.setExpireDate(expireDate);

            // 执行搜索
            Page<Medicine> medicinesPage = medicineService.searchMedicines(params);

            // 构建返回结果
            PageResult<Medicine> pageResult = new PageResult<>();
            pageResult.setRecords(medicinesPage.getContent());
            pageResult.setTotal((int) medicinesPage.getTotalElements());
            pageResult.setCurrent(page);
            pageResult.setSize(size);

            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("获取药品列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Medicine get(@PathVariable Long id) {
        return medicineService.findById(id);
    }

    @PostMapping("/add")
    public Medicine add(@RequestBody Medicine medicine) {
        return medicineService.save(medicine);
    }

    @PutMapping("/update/{id}")
    public Medicine update(@PathVariable Long id, @RequestBody Medicine medicine) {
        medicine.setId(id);
        return medicineService.save(medicine);
    }

    @DeleteMapping("/delete/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        try {
            medicineService.deleteById(id);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/category/list")
    public Result<List<MedicineCategory>> getCategoryList() {
        try {
            List<MedicineCategory> categories = categoryService.findAll();
            return Result.success(categories);
        } catch (Exception e) {
            return Result.error("获取药品分类失败: " + e.getMessage());
        }
    }
}