<template>
  <div class="search-form">
    <!-- 简单搜索模式 -->
    <div v-if="!advancedMode" class="simple-search">
      <el-input
        v-model="formData.keyword"
        placeholder="请输入关键字搜索"
        clearable
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
      <el-button type="primary" text @click="toggleAdvancedMode">
        高级搜索
        <el-icon><ArrowDown /></el-icon>
      </el-button>
    </div>

    <!-- 高级搜索模式 -->
    <el-form
      v-else
      ref="formRef"
      :model="formData"
      label-width="80px"
      class="advanced-search"
    >
      <el-row :gutter="20">
        <!-- 动态渲染搜索字段 -->
        <el-col
          v-for="field in searchConfig.fields"
          :key="field.prop"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
        >
          <el-form-item :label="field.label">
            <!-- 输入框 -->
            <el-input
              v-if="field.type === 'input'"
              v-model="formData[field.prop]"
              :placeholder="'请输入' + field.label"
              clearable
            />

            <!-- 选择器 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="formData[field.prop]"
              :placeholder="'请选择' + field.label"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="field.type === 'date'"
              v-model="formData[field.prop]"
              type="date"
              :placeholder="'请选择' + field.label"
              style="width: 100%"
            />

            <!-- 日期范围选择器 -->
            <el-date-picker
              v-else-if="field.type === 'daterange'"
              v-model="formData[field.prop]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />

            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="formData[field.prop]"
              :placeholder="field.placeholder || '请输入' + field.label"
              style="width: 100%"
            />

            <!-- 数字范围输入框 -->
            <div v-else-if="field.type === 'number-range'" class="number-range">
              <el-input-number
                v-model="formData[field.prop + 'Min']"
                :placeholder="field.placeholder?.[0] || '最小值'"
                style="width: 48%"
              />
              <span class="range-separator">-</span>
              <el-input-number
                v-model="formData[field.prop + 'Max']"
                :placeholder="field.placeholder?.[1] || '最大值'"
                style="width: 48%"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="form-actions">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="text" @click="toggleAdvancedMode">
          收起
          <el-icon><ArrowUp /></el-icon>
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { Search, ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import type { FormInstance } from 'element-plus';

// 定义搜索字段类型
interface SearchField {
  label: string;
  prop: string;
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'number-range';
  options?: Array<{ label: string; value: any }>;
  defaultValue?: any;
  placeholder?: string | string[];
}

// 定义搜索配置类型
interface SearchConfig {
  fields: SearchField[];
}

// 定义组件的props
const props = defineProps<{
  searchConfig: SearchConfig;
  initialValues?: Record<string, any>;
}>();

// 定义组件的事件
const emit = defineEmits<{
  (e: 'search', params: Record<string, any>): void;
  (e: 'reset'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 是否为高级搜索模式
const advancedMode = ref(false);

// 表单数据
const formData = reactive<Record<string, any>>({
  keyword: '',
});

// 初始化表单数据
onMounted(() => {
  // 初始化表单字段
  props.searchConfig.fields.forEach((field) => {
    formData[field.prop] = field.defaultValue !== undefined ? field.defaultValue : '';
  });

  // 应用初始值
  if (props.initialValues) {
    Object.keys(props.initialValues).forEach((key) => {
      if (key in formData) {
        formData[key] = props.initialValues[key];
      }
    });
  }
});

// 监听初始值变化
watch(
  () => props.initialValues,
  (newValues) => {
    if (newValues) {
      Object.keys(newValues).forEach((key) => {
        if (key in formData) {
          formData[key] = newValues[key];
        }
      });
    }
  },
  { deep: true }
);

// 切换高级搜索模式
const toggleAdvancedMode = () => {
  advancedMode.value = !advancedMode.value;
};

// 处理搜索
const handleSearch = () => {
  const params: Record<string, any> = {};

  // 收集非空值
  Object.keys(formData).forEach((key) => {
    const value = formData[key];
    if (value !== '' && value !== null && value !== undefined) {
      params[key] = value;
    }
  });

  emit('search', params);
};

// 处理重置
const handleReset = () => {
  // 重置表单数据
  props.searchConfig.fields.forEach((field) => {
    formData[field.prop] = field.defaultValue !== undefined ? field.defaultValue : '';
  });
  formData.keyword = '';

  emit('reset');
};
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.simple-search {
  display: flex;
  align-items: center;
  gap: 10px;
}

.simple-search .el-input {
  max-width: 500px;
}

.advanced-search {
  padding-top: 10px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.number-range {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.range-separator {
  color: #909399;
  font-weight: 500;
}
</style>