<template>
  <el-dialog
    :model-value="visible"
    :title="`${medicineName} - 库存记录`"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
    @update:model-value="$emit('update:visible', $event)"
  >
    <div class="records-container">
      <!-- 搜索筛选 -->
      <div class="search-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select
              v-model="searchParams.type"
              placeholder="操作类型"
              clearable
              style="width: 100%"
            >
              <el-option label="全部" value="" />
              <el-option label="入库" value="IN" />
              <el-option label="出库" value="OUT" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="searchParams.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="searchParams.operator"
              placeholder="操作人"
              clearable
            />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="fetchRecords">搜索</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 记录列表 -->
      <div class="records-list">
        <el-table
          v-loading="loading"
          :data="recordsList"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column label="操作类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.type === 'IN' ? 'success' : 'warning'">
                {{ row.type === 'IN' ? '入库' : '出库' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="quantity" label="数量" width="80">
            <template #default="{ row }">
              <span :class="row.type === 'IN' ? 'quantity-in' : 'quantity-out'">
                {{ row.type === 'IN' ? '+' : '-' }}{{ row.quantity }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column label="库存变化" width="120">
            <template #default="{ row }">
              <div class="stock-change">
                <span>{{ row.beforeStock }}</span>
                <el-icon><ArrowRight /></el-icon>
                <span>{{ row.afterStock }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="reason" label="操作原因" min-width="150" />
          
          <el-table-column prop="operatorName" label="操作人" width="100" />
          
          <el-table-column prop="createTime" label="操作时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="fetchRecords"
          @current-change="fetchRecords"
        />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportRecords">导出记录</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowRight } from '@element-plus/icons-vue';
import { getInventoryRecords } from '@/api/modules/inventory';
import type { InventoryRecord } from '@/types';

// 定义组件的props
const props = defineProps<{
  visible: boolean;
  medicineId?: number;
  medicineName?: string;
}>();

// 定义组件的事件
const emit = defineEmits(['update:visible']);

// 响应式数据
const loading = ref(false);
const recordsList = ref<InventoryRecord[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 搜索参数
const searchParams = reactive({
  type: '',
  dateRange: null as any,
  operator: ''
});

// 时间格式化函数
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取记录列表
const fetchRecords = async () => {
  if (!props.medicineId) return;
  
  loading.value = true;
  try {
    const params = {
      medicineId: props.medicineId,
      page: currentPage.value,
      size: pageSize.value,
      type: searchParams.type,
      operator: searchParams.operator,
      startDate: searchParams.dateRange?.[0],
      endDate: searchParams.dateRange?.[1]
    };
    
    const result = await getInventoryRecords(params);
    recordsList.value = result.records || result.data || [];
    total.value = result.total || 0;
  } catch (error: any) {
    console.error('获取库存记录失败:', error);
    ElMessage.error(error.msg || '获取库存记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
};

// 导出记录
const exportRecords = () => {
  ElMessage.info('导出功能开发中...');
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.medicineId) {
    currentPage.value = 1;
    fetchRecords();
  }
});
</script>

<style scoped>
.records-container {
  padding: 16px 0;
}

.search-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.records-list {
  margin-bottom: 20px;
}

.quantity-in {
  color: #67c23a;
  font-weight: 600;
}

.quantity-out {
  color: #f56c6c;
  font-weight: 600;
}

.stock-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
</style>
