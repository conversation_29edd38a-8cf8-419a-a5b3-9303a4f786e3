<template>
  <div class="inventory-form">
    <div class="form-header">
      <h3>{{ formTitle }}</h3>
      <p class="form-description">{{ formDescription }}</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="药品" prop="medicineId">
            <el-select
              v-model="formData.medicineId"
              filterable
              remote
              reserve-keyword
              placeholder="请选择或搜索药品"
              style="width: 100%"
              :disabled="!!props.medicineId"
              :loading="medicineLoading"
              @change="handleMedicineChange"
            >
              <el-option
                v-for="item in medicineOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <div class="medicine-option">
                  <span class="medicine-name">{{ item.label }}</span>
                  <span class="medicine-stock">库存: {{ item.stock || 0 }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="仓库" prop="warehouseId">
            <el-select
              v-model="formData.warehouseId"
              placeholder="请选择仓库"
              style="width: 100%"
            >
              <el-option
                v-for="item in warehouseOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number
              v-model="formData.quantity"
              :min="1"
              :max="props.type === 'out' ? currentStock : undefined"
              :precision="0"
              style="width: 100%"
              controls-position="right"
            />
            <div v-if="props.type === 'out' && currentStock > 0" class="stock-hint">
              当前库存: {{ currentStock }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="操作类型">
            <el-tag :type="props.type === 'in' ? 'success' : 'warning'" size="large">
              {{ props.type === 'in' ? '入库操作' : '出库操作' }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="操作原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请详细说明本次操作的原因..."
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ submitting ? '提交中...' : '确认' + (props.type === 'in' ? '入库' : '出库') }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';

// 定义组件的props
const props = defineProps<{
  medicineId?: number;
  type: 'in' | 'out';
}>();

// 定义组件的事件
const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 状态管理
const submitting = ref(false);
const medicineLoading = ref(false);
const currentStock = ref(0);

// 表单数据
const formData = reactive({
  medicineId: props.medicineId || '',
  warehouseId: 1, // 默认选择主仓库
  quantity: 1,
  reason: ''
});

// 计算属性
const formTitle = computed(() => {
  return props.type === 'in' ? '药品入库' : '药品出库';
});

const formDescription = computed(() => {
  return props.type === 'in'
    ? '请填写入库信息，确保数据准确无误'
    : '请填写出库信息，注意不能超过当前库存';
});

// 表单验证规则
const rules = reactive<FormRules>({
  medicineId: [
    { required: true, message: '请选择药品', trigger: 'change' }
  ],
  warehouseId: [
    { required: true, message: '请选择仓库', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' },
    {
      validator: (rule: any, value: number, callback: any) => {
        if (props.type === 'out' && value > currentStock.value) {
          callback(new Error(`出库数量不能超过当前库存 ${currentStock.value}`));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: '请输入操作原因', trigger: 'blur' },
    { min: 5, message: '操作原因至少5个字符', trigger: 'blur' }
  ]
});

// 药品选项
const medicineOptions = ref<{ label: string; value: number; stock?: number }[]>([
  { label: '阿莫西林胶囊', value: 1, stock: 1000 },
  { label: '布洛芬片', value: 2, stock: 500 },
  { label: '感冒灵颗粒', value: 3, stock: 800 },
  { label: '维生素C片', value: 4, stock: 300 },
  { label: '板蓝根颗粒', value: 5, stock: 150 }
]);

// 处理药品选择变化
const handleMedicineChange = (medicineId: number) => {
  const selectedMedicine = medicineOptions.value.find(item => item.value === medicineId);
  if (selectedMedicine) {
    currentStock.value = selectedMedicine.stock || 0;
    // 如果是出库操作，重置数量为1
    if (props.type === 'out') {
      formData.quantity = 1;
    }
  }
};

// 仓库选项
const warehouseOptions = ref<{ label: string; value: number }[]>([
  { label: '主仓库', value: 1 },
  { label: '备用仓库', value: 2 }
]);

// 导入真实的API函数
import { inventoryIn, inventoryOut } from '@/api/modules/inventory';

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true;
      try {
        // 构建提交数据
        const submitData = {
          medicineId: formData.medicineId,
          warehouseId: formData.warehouseId,
          quantity: formData.quantity,
          reason: formData.reason
        };

        // 根据操作类型调用不同的API
        if (props.type === 'in') {
          await inventoryIn(submitData);
          ElMessage.success({
            message: `成功入库 ${formData.quantity} 件`,
            type: 'success',
            duration: 3000
          });
        } else {
          await inventoryOut(submitData);
          ElMessage.success({
            message: `成功出库 ${formData.quantity} 件`,
            type: 'success',
            duration: 3000
          });
        }

        emit('success');
      } catch (error: any) {
        console.error('库存操作失败:', error);
        ElMessage.error({
          message: error.msg || error.message || '操作失败，请重试',
          duration: 5000
        });
      } finally {
        submitting.value = false;
      }
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
};

// 页面加载时初始化
onMounted(() => {
  // 如果有指定的药品ID，设置表单的药品ID
  if (props.medicineId) {
    formData.medicineId = props.medicineId;
  }
});
</script>

<style scoped>
.inventory-form {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  min-height: 500px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-header h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.form-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.medicine-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.medicine-name {
  font-weight: 500;
  color: #2c3e50;
}

.medicine-stock {
  font-size: 12px;
  color: #7f8c8d;
  background: #ecf0f1;
  padding: 2px 8px;
  border-radius: 12px;
}

.stock-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #e67e22;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.el-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .inventory-form {
    padding: 16px;
  }

  .form-header {
    padding: 16px;
    margin-bottom: 20px;
  }

  .el-form {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>