package com.example.medicine.dto;

import lombok.Data;
import java.util.Date;

@Data
public class MedicineSearchParams {
    // 关键字搜索（在药品名称、规格、批号中模糊搜索）
    private String keyword;
    
    // 精确搜索字段
    private String name;         // 药品名称
    private Long categoryId;     // 分类ID
    private String spec;         // 规格
    private String batchNo;      // 批号
    private Integer status;      // 状态（前端传数字：1-正常，0-停用）
    private Date expireDate;     // 有效期
    
    // 分页参数
    private int page = 1;
    private int size = 10;
    
    /**
     * 将前端传入的数字状态转换为数据库的字符串状态
     * 1 -> "ONSALE" (正常)
     * 0 -> "OFFSALE" (停用)
     */
    public String getStatusAsString() {
        if (status == null) {
            return null;
        }
        return status == 1 ? "ONSALE" : "OFFSALE";
    }
}
