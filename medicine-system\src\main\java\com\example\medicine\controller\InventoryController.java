package com.example.medicine.controller;

import com.example.medicine.dto.InventoryOperationRequest;
import com.example.medicine.entity.Inventory;
import com.example.medicine.entity.InventoryRecord;
import com.example.medicine.service.InventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/inventory")
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;

    @GetMapping("/list")
    public List<Inventory> list() {
        return inventoryService.findAll();
    }

    @GetMapping("/{id}")
    public Inventory get(@PathVariable Long id) {
        return inventoryService.findById(id);
    }

    @PostMapping("/add")
    public Inventory add(@RequestBody Inventory inventory) {
        return inventoryService.save(inventory);
    }

    @PutMapping("/update/{id}")
    public Inventory update(@PathVariable Long id, @RequestBody Inventory inventory) {
        inventory.setId(id);
        return inventoryService.save(inventory);
    }

    @DeleteMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        inventoryService.deleteById(id);
    }

    @PostMapping("/in")
    public ResponseEntity<String> inventoryIn(@RequestBody InventoryOperationRequest request) {
        try {
            inventoryService.inventoryIn(request);
            return ResponseEntity.ok("入库成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("入库失败: " + e.getMessage());
        }
    }

    @PostMapping("/out")
    public ResponseEntity<String> inventoryOut(@RequestBody InventoryOperationRequest request) {
        try {
            inventoryService.inventoryOut(request);
            return ResponseEntity.ok("出库成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("出库失败: " + e.getMessage());
        }
    }

    @GetMapping("/records")
    public ResponseEntity<Map<String, Object>> getInventoryRecords(
            @RequestParam(required = false) Long medicineId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String operator,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            Pageable pageable = PageRequest.of(page - 1, size);
            Page<InventoryRecord> recordsPage = inventoryService.getInventoryRecords(
                medicineId, type, operator, startDate, endDate, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("records", recordsPage.getContent());
            response.put("total", recordsPage.getTotalElements());
            response.put("current", page);
            response.put("size", size);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取库存记录失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
}