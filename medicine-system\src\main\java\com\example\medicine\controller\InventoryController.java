package com.example.medicine.controller;

import com.example.medicine.dto.InventoryOperationRequest;
import com.example.medicine.entity.Inventory;
import com.example.medicine.service.InventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/inventory")
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;

    @GetMapping("/list")
    public List<Inventory> list() {
        return inventoryService.findAll();
    }

    @GetMapping("/{id}")
    public Inventory get(@PathVariable Long id) {
        return inventoryService.findById(id);
    }

    @PostMapping("/add")
    public Inventory add(@RequestBody Inventory inventory) {
        return inventoryService.save(inventory);
    }

    @PutMapping("/update/{id}")
    public Inventory update(@PathVariable Long id, @RequestBody Inventory inventory) {
        inventory.setId(id);
        return inventoryService.save(inventory);
    }

    @DeleteMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        inventoryService.deleteById(id);
    }

    @PostMapping("/in")
    public ResponseEntity<String> inventoryIn(@RequestBody InventoryOperationRequest request) {
        try {
            inventoryService.inventoryIn(request);
            return ResponseEntity.ok("入库成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("入库失败: " + e.getMessage());
        }
    }

    @PostMapping("/out")
    public ResponseEntity<String> inventoryOut(@RequestBody InventoryOperationRequest request) {
        try {
            inventoryService.inventoryOut(request);
            return ResponseEntity.ok("出库成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("出库失败: " + e.getMessage());
        }
    }
}