package com.example.medicine.service;

import com.example.medicine.dto.MedicineSearchParams;
import com.example.medicine.entity.Medicine;
import org.springframework.data.domain.Page;
import java.util.List;

public interface MedicineService {
    List<Medicine> findAll();
    Medicine findById(Long id);
    Medicine save(Medicine medicine);
    void deleteById(Long id);

    /**
     * 根据搜索参数分页查询药品
     */
    Page<Medicine> searchMedicines(MedicineSearchParams params);
}