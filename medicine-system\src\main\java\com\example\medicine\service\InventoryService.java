package com.example.medicine.service;

import com.example.medicine.dto.InventoryOperationRequest;
import com.example.medicine.entity.Inventory;
import java.util.List;

public interface InventoryService {
    List<Inventory> findAll();
    Inventory findById(Long id);
    Inventory save(Inventory inventory);
    void deleteById(Long id);

    // 入库操作
    void inventoryIn(InventoryOperationRequest request);

    // 出库操作
    void inventoryOut(InventoryOperationRequest request);
}