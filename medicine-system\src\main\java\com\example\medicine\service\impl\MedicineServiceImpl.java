package com.example.medicine.service.impl;

import com.example.medicine.dto.MedicineSearchParams;
import com.example.medicine.entity.Medicine;
import com.example.medicine.repository.MedicineRepository;
import com.example.medicine.service.MedicineService;
import com.example.medicine.specification.MedicineSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MedicineServiceImpl implements MedicineService {

    @Autowired
    private MedicineRepository medicineRepository;

    @Override
    public List<Medicine> findAll() {
        return medicineRepository.findAll();
    }

    @Override
    public Medicine findById(Long id) {
        return medicineRepository.findById(id).orElse(null);
    }

    @Override
    public Medicine save(Medicine medicine) {
        return medicineRepository.save(medicine);
    }

    @Override
    public void deleteById(Long id) {
        medicineRepository.deleteById(id);
    }

    @Override
    public Page<Medicine> searchMedicines(MedicineSearchParams params) {
        // 创建分页对象，按ID降序排列
        Pageable pageable = PageRequest.of(
            params.getPage() - 1, // Spring Data JPA 的页码从0开始
            params.getSize(),
            Sort.by(Sort.Direction.DESC, "id")
        );

        // 构建查询条件
        Specification<Medicine> specification = MedicineSpecification.buildSpecification(params);

        // 执行分页查询
        return medicineRepository.findAll(specification, pageable);
    }
}