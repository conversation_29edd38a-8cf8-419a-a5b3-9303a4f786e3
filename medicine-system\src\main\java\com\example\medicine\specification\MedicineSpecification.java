package com.example.medicine.specification;

import com.example.medicine.dto.MedicineSearchParams;
import com.example.medicine.entity.Medicine;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

public class MedicineSpecification {

    /**
     * 根据搜索参数构建动态查询条件
     */
    public static Specification<Medicine> buildSpecification(MedicineSearchParams params) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关键字搜索：在药品名称、规格、批号中模糊搜索
            if (StringUtils.hasText(params.getKeyword())) {
                String keyword = "%" + params.getKeyword() + "%";
                Predicate nameLike = criteriaBuilder.like(root.get("name"), keyword);
                Predicate specLike = criteriaBuilder.like(root.get("spec"), keyword);
                Predicate batchNoLike = criteriaBuilder.like(root.get("batchNo"), keyword);
                
                // 使用 OR 连接这三个条件
                predicates.add(criteriaBuilder.or(nameLike, specLike, batchNoLike));
            }

            // 药品名称模糊搜索
            if (StringUtils.hasText(params.getName())) {
                predicates.add(criteriaBuilder.like(root.get("name"), "%" + params.getName() + "%"));
            }

            // 分类精确匹配
            if (params.getCategoryId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("categoryId"), params.getCategoryId()));
            }

            // 规格模糊搜索
            if (StringUtils.hasText(params.getSpec())) {
                predicates.add(criteriaBuilder.like(root.get("spec"), "%" + params.getSpec() + "%"));
            }

            // 批号模糊搜索
            if (StringUtils.hasText(params.getBatchNo())) {
                predicates.add(criteriaBuilder.like(root.get("batchNo"), "%" + params.getBatchNo() + "%"));
            }

            // 状态精确匹配
            if (params.getStatus() != null) {
                String statusString = params.getStatusAsString();
                predicates.add(criteriaBuilder.equal(root.get("status"), statusString));
            }

            // 有效期搜索（大于等于指定日期）
            if (params.getExpireDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("expireDate"), params.getExpireDate()));
            }

            // 将所有条件用 AND 连接
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
