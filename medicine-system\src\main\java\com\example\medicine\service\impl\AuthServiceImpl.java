package com.example.medicine.service.impl;

import com.example.medicine.dto.ChangePasswordRequest;
import com.example.medicine.dto.LoginRequest;
import com.example.medicine.dto.LoginResponse;
import com.example.medicine.dto.UserProfileDto;
import com.example.medicine.dto.UserSettingsDto;
import com.example.medicine.entity.User;
import com.example.medicine.repository.UserRepository;
import com.example.medicine.service.AuthService;
import com.example.medicine.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AuthServiceImpl implements AuthService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private JwtUtil jwtUtil;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        // 根据用户名查找用户
        User user = userRepository.findByUsername(loginRequest.getUsername());
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 检查用户状态
        if (!"active".equals(user.getStatus())) {
            throw new RuntimeException("用户账号已被禁用");
        }

        // 验证密码 - 直接比较明文密码
        if (!loginRequest.getPassword().equals(user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 生成JWT token
        String token = jwtUtil.generateToken(user.getUsername(), loginRequest.getRememberMe());
        
        // 构建响应
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
            user.getId(),
            user.getUsername(),
            getRoleName(user.getRoleId()),
            user.getStatus()
        );
        
        return new LoginResponse(token, userInfo);
    }
    
    @Override
    public void logout(String token) {
        // 这里可以实现token黑名单机制
        // 目前简单实现，客户端删除token即可
    }

    @Override
    public UserProfileDto getUserProfile(String token) {
        try {
            // 从token中获取用户名
            String username = getUsernameFromToken(token);
            User user = userRepository.findByUsername(username);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 转换为UserProfileDto
            UserProfileDto profile = new UserProfileDto();
            profile.setId(user.getId());
            profile.setUsername(user.getUsername());
            profile.setEmail(user.getEmail());
            profile.setPhone(user.getPhone());
            profile.setAvatar(user.getAvatar());
            profile.setRealName(user.getRealName());
            profile.setDepartment(user.getDepartment());
            profile.setPosition(user.getPosition());
            profile.setRoleName(getRoleName(user.getRoleId()));
            profile.setCreateTime(user.getCreateTime());
            profile.setLastLoginTime(user.getLastLoginTime());

            return profile;
        } catch (Exception e) {
            throw new RuntimeException("获取用户信息失败: " + e.getMessage());
        }
    }

    @Override
    public UserProfileDto updateUserProfile(String token, UserProfileDto profileDto) {
        try {
            // 从token中获取用户名
            String username = getUsernameFromToken(token);
            User user = userRepository.findByUsername(username);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 更新用户信息
            if (profileDto.getUsername() != null) {
                user.setUsername(profileDto.getUsername());
            }
            if (profileDto.getEmail() != null) {
                user.setEmail(profileDto.getEmail());
            }
            if (profileDto.getPhone() != null) {
                user.setPhone(profileDto.getPhone());
            }
            if (profileDto.getAvatar() != null) {
                user.setAvatar(profileDto.getAvatar());
            }
            if (profileDto.getRealName() != null) {
                user.setRealName(profileDto.getRealName());
            }
            if (profileDto.getDepartment() != null) {
                user.setDepartment(profileDto.getDepartment());
            }
            if (profileDto.getPosition() != null) {
                user.setPosition(profileDto.getPosition());
            }

            // 保存更新
            user = userRepository.save(user);

            // 返回更新后的profile
            return getUserProfile(token);
        } catch (Exception e) {
            throw new RuntimeException("更新用户信息失败: " + e.getMessage());
        }
    }

    private String getUsernameFromToken(String token) {
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        return jwtUtil.getUsernameFromToken(token);
    }

    private String getRoleName(Long roleId) {
        // 根据角色ID返回角色名称
        switch (roleId.intValue()) {
            case 1: return "超级管理员";
            case 2: return "管理员";
            case 3: return "采购员";
            case 4: return "销售员";
            case 5: return "仓库管理员";
            default: return "普通用户";
        }
    }

    @Override
    public UserSettingsDto getUserSettings(String token) {
        try {
            String username = getUsernameFromToken(token);
            User user = userRepository.findByUsername(username);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 如果用户没有设置，返回默认设置
            if (user.getUserSettings() == null || user.getUserSettings().isEmpty()) {
                return getDefaultSettings();
            }

            // 解析JSON字符串为UserSettingsDto
            return objectMapper.readValue(user.getUserSettings(), UserSettingsDto.class);
        } catch (Exception e) {
            // 如果解析失败，返回默认设置
            return getDefaultSettings();
        }
    }

    @Override
    public UserSettingsDto updateUserSettings(String token, UserSettingsDto settingsDto) {
        try {
            String username = getUsernameFromToken(token);
            User user = userRepository.findByUsername(username);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 将设置对象转换为JSON字符串存储
            String settingsJson = objectMapper.writeValueAsString(settingsDto);
            user.setUserSettings(settingsJson);
            userRepository.save(user);

            return settingsDto;
        } catch (Exception e) {
            throw new RuntimeException("更新用户设置失败: " + e.getMessage());
        }
    }

    private UserSettingsDto getDefaultSettings() {
        UserSettingsDto settings = new UserSettingsDto();
        settings.setTheme("light");
        settings.setLanguage("zh-CN");

        UserSettingsDto.NotificationSettings notifications = new UserSettingsDto.NotificationSettings();
        notifications.setEmail(true);
        notifications.setSystem(true);
        notifications.setLowStock(true);
        notifications.setExpiring(true);
        settings.setNotifications(notifications);

        UserSettingsDto.LayoutSettings layout = new UserSettingsDto.LayoutSettings();
        layout.setSidebarCollapsed(false);
        layout.setShowBreadcrumb(true);
        layout.setShowTabs(true);
        settings.setLayout(layout);

        return settings;
    }

    @Override
    public void changePassword(String token, ChangePasswordRequest changePasswordRequest) {
        try {
            // 验证输入参数
            if (changePasswordRequest.getCurrentPassword() == null || changePasswordRequest.getCurrentPassword().trim().isEmpty()) {
                throw new RuntimeException("当前密码不能为空");
            }
            if (changePasswordRequest.getNewPassword() == null || changePasswordRequest.getNewPassword().trim().isEmpty()) {
                throw new RuntimeException("新密码不能为空");
            }
            if (changePasswordRequest.getConfirmPassword() == null || changePasswordRequest.getConfirmPassword().trim().isEmpty()) {
                throw new RuntimeException("确认密码不能为空");
            }

            // 验证新密码和确认密码是否一致
            if (!changePasswordRequest.getNewPassword().equals(changePasswordRequest.getConfirmPassword())) {
                throw new RuntimeException("新密码和确认密码不一致");
            }

            // 验证新密码长度
            if (changePasswordRequest.getNewPassword().length() < 8) {
                throw new RuntimeException("新密码长度不能少于8位");
            }

            // 从token中获取用户名
            String username = getUsernameFromToken(token);
            User user = userRepository.findByUsername(username);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 验证当前密码是否正确（目前使用明文比较，与登录逻辑保持一致）
            if (!changePasswordRequest.getCurrentPassword().equals(user.getPassword())) {
                throw new RuntimeException("当前密码错误");
            }

            // 验证新密码不能与当前密码相同
            if (changePasswordRequest.getNewPassword().equals(user.getPassword())) {
                throw new RuntimeException("新密码不能与当前密码相同");
            }

            // 更新密码（目前使用明文存储，与现有逻辑保持一致）
            user.setPassword(changePasswordRequest.getNewPassword());
            userRepository.save(user);

        } catch (Exception e) {
            throw new RuntimeException("修改密码失败: " + e.getMessage());
        }
    }
}
