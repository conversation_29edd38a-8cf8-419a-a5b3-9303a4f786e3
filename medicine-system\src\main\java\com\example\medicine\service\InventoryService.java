package com.example.medicine.service;

import com.example.medicine.dto.InventoryOperationRequest;
import com.example.medicine.entity.Inventory;
import com.example.medicine.entity.InventoryRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.Date;
import java.util.List;

public interface InventoryService {
    List<Inventory> findAll();
    Inventory findById(Long id);
    Inventory save(Inventory inventory);
    void deleteById(Long id);

    // 入库操作
    void inventoryIn(InventoryOperationRequest request);

    // 出库操作
    void inventoryOut(InventoryOperationRequest request);

    // 获取库存记录
    Page<InventoryRecord> getInventoryRecords(Long medicineId, String type, String operator,
                                            Date startDate, Date endDate, Pageable pageable);
}