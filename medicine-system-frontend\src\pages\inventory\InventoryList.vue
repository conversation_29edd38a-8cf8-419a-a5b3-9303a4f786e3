<template>
  <div class="inventory-list-page">
    <div class="page-header">
      <h2>库存管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleInStock">
          <el-icon><Plus /></el-icon>入库
        </el-button>
        <el-button type="warning" @click="handleOutStock">
          <el-icon><Minus /></el-icon>出库
        </el-button>
        <el-button type="danger" @click="showAlerts">
          <el-icon><Warning /></el-icon>库存预警
        </el-button>
        <el-button @click="fetchInventoryList">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
      </div>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="inventoryList"
        border
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'lastUpdate', order: 'descending' }"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="medicineName" label="药品名称" min-width="140" sortable="custom">
          <template #default="{ row }">
            <div class="medicine-info">
              <span class="medicine-name">{{ row.medicineName }}</span>
              <span class="medicine-id">ID: {{ row.medicineId }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="warehouseName" label="仓库" width="120" sortable="custom" />

        <el-table-column prop="quantity" label="库存数量" width="120" sortable="custom">
          <template #default="{ row }">
            <div class="quantity-info">
              <span :class="getStockStatusClass(row.quantity)">{{ row.quantity }}</span>
              <el-tag
                :type="getStockTagType(row.quantity)"
                size="small"
                class="stock-tag"
              >
                {{ getStockStatusText(row.quantity) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="lastUpdate" label="最后更新时间" width="180" sortable="custom">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon><Clock /></el-icon>
              <span>{{ formatDateTime(row.lastUpdate) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                :icon="Plus"
                @click="handleInStockItem(row)"
                class="action-btn"
              >
                入库
              </el-button>
              <el-button
                type="warning"
                size="small"
                :icon="Minus"
                @click="handleOutStockItem(row)"
                class="action-btn"
              >
                出库
              </el-button>
              <el-button
                type="info"
                size="small"
                :icon="Document"
                @click="handleViewRecord(row)"
                class="action-btn"
              >
                记录
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 入库/出库对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <InventoryForm
        :medicine-id="selectedMedicineId"
        :type="formType"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>

    <!-- 库存记录对话框 -->
    <InventoryRecords
      v-model:visible="recordsVisible"
      :medicine-id="selectedMedicine?.id"
      :medicine-name="selectedMedicine?.name"
    />

    <!-- 库存预警对话框 -->
    <el-dialog
      v-model="alertVisible"
      title="库存预警管理"
      width="90%"
      destroy-on-close
    >
      <InventoryAlert />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Minus, Search, Refresh, Clock, Document, Warning } from '@element-plus/icons-vue';
import type { Inventory, PageParams } from '@/types';
import { getInventoryList } from '@/api/modules/inventory';
import SearchForm from '@/components/common/SearchForm.vue';
import InventoryForm from './InventoryForm.vue';
import InventoryRecords from './InventoryRecords.vue';
import InventoryAlert from './InventoryAlert.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    {
      label: '药品名称',
      prop: 'medicineName',
      type: 'input',
      placeholder: '请输入药品名称'
    },
    {
      label: '仓库',
      prop: 'warehouseId',
      type: 'select',
      placeholder: '请选择仓库',
      options: [
        { label: '全部仓库', value: '' },
        { label: '主仓库', value: 1 },
        { label: '备用仓库', value: 2 }
      ]
    },
    {
      label: '库存状态',
      prop: 'stockStatus',
      type: 'select',
      placeholder: '请选择库存状态',
      options: [
        { label: '全部状态', value: '' },
        { label: '库存充足', value: 'sufficient' },
        { label: '库存不足', value: 'insufficient' },
        { label: '库存紧急', value: 'urgent' }
      ]
    },
    {
      label: '库存数量',
      prop: 'quantityRange',
      type: 'number-range',
      placeholder: ['最小数量', '最大数量']
    }
  ]
});

// 数据列表
const inventoryList = ref<Inventory[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formType = ref<'in' | 'out'>('in');
const selectedMedicineId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  return formType.value === 'in' ? '药品入库' : '药品出库';
});

// 库存记录对话框相关
const recordsVisible = ref(false);
const selectedMedicine = ref<{ id: number; name: string } | null>(null);

// 预警对话框相关
const alertVisible = ref(false);

// 库存状态判断函数
const getStockStatusClass = (quantity: number) => {
  if (quantity <= 10) return 'stock-urgent';
  if (quantity <= 50) return 'stock-insufficient';
  return 'stock-sufficient';
};

const getStockTagType = (quantity: number) => {
  if (quantity <= 10) return 'danger';
  if (quantity <= 50) return 'warning';
  return 'success';
};

const getStockStatusText = (quantity: number) => {
  if (quantity <= 10) return '紧急';
  if (quantity <= 50) return '不足';
  return '充足';
};

// 时间格式化函数
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取库存列表
const fetchInventoryList = async () => {
  loading.value = true;
  try {
    const result = await getInventoryList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });

    // 处理不同的响应格式
    if (result && typeof result === 'object') {
      // 标准分页响应格式
      if ('records' in result) {
        inventoryList.value = result.records || [];
        total.value = result.total || 0;
      }
      // 简单数组格式
      else if (Array.isArray(result)) {
        inventoryList.value = result;
        total.value = result.length;
      }
      // 其他格式
      else {
        inventoryList.value = result.data || result.list || [];
        total.value = result.total || result.count || 0;
      }
    } else {
      inventoryList.value = [];
      total.value = 0;
    }
  } catch (error: any) {
    console.error('获取库存列表失败:', error);
    ElMessage.error(error.msg || error.message || '获取库存列表失败');
    inventoryList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  // 清空之前的搜索参数
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });

  // 更新搜索参数
  Object.keys(params).forEach(key => {
    if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
      // 处理数字范围搜索
      if (key === 'quantityRangeMin' && params[key]) {
        searchParams.minQuantity = params[key];
      } else if (key === 'quantityRangeMax' && params[key]) {
        searchParams.maxQuantity = params[key];
      } else {
        searchParams[key as keyof PageParams] = params[key];
      }
    }
  });

  currentPage.value = 1; // 重置到第一页
  fetchInventoryList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchInventoryList();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchInventoryList();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
  fetchInventoryList();
};

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  if (order) {
    searchParams.sortField = prop;
    searchParams.sortOrder = order === 'ascending' ? 'asc' : 'desc';
  } else {
    delete searchParams.sortField;
    delete searchParams.sortOrder;
  }
  currentPage.value = 1;
  fetchInventoryList();
};

// 处理入库
const handleInStock = () => {
  formType.value = 'in';
  selectedMedicineId.value = undefined;
  dialogVisible.value = true;
};

// 处理出库
const handleOutStock = () => {
  formType.value = 'out';
  selectedMedicineId.value = undefined;
  dialogVisible.value = true;
};

// 处理单个药品入库
const handleInStockItem = (row: Inventory) => {
  formType.value = 'in';
  selectedMedicineId.value = row.medicineId;
  dialogVisible.value = true;
};

// 处理单个药品出库
const handleOutStockItem = (row: Inventory) => {
  formType.value = 'out';
  selectedMedicineId.value = row.medicineId;
  dialogVisible.value = true;
};

// 处理查看记录
const handleViewRecord = (row: Inventory) => {
  selectedMedicine.value = {
    id: row.medicineId,
    name: row.medicineName || '未知药品'
  };
  recordsVisible.value = true;
};

// 显示预警
const showAlerts = () => {
  alertVisible.value = true;
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchInventoryList(); // 重新加载列表
};

// 页面加载时获取数据
onMounted(() => {
  fetchInventoryList();
});
</script>

<style scoped>
.inventory-list-page {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.table-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.medicine-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.medicine-name {
  font-weight: 600;
  color: #2c3e50;
}

.medicine-id {
  font-size: 12px;
  color: #7f8c8d;
}

.quantity-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stock-sufficient {
  color: #27ae60;
  font-weight: 600;
}

.stock-insufficient {
  color: #f39c12;
  font-weight: 600;
}

.stock-urgent {
  color: #e74c3c;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.stock-tag {
  font-size: 10px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #7f8c8d;
  font-size: 13px;
}

.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
}

.action-btn {
  min-width: 60px;
  height: 28px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .inventory-list-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }

  .action-btn {
    min-width: auto;
    width: 100%;
    font-size: 11px;
  }

  .table-container {
    padding: 12px;
  }
}
</style>
