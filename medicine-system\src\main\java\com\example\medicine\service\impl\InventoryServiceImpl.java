package com.example.medicine.service.impl;

import com.example.medicine.dto.InventoryOperationRequest;
import com.example.medicine.entity.Inventory;
import com.example.medicine.entity.InventoryRecord;
import com.example.medicine.repository.InventoryRepository;
import com.example.medicine.repository.InventoryRecordRepository;
import com.example.medicine.service.InventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class InventoryServiceImpl implements InventoryService {

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private InventoryRecordRepository inventoryRecordRepository;

    @Override
    public List<Inventory> findAll() {
        return inventoryRepository.findAll();
    }

    @Override
    public Inventory findById(Long id) {
        return inventoryRepository.findById(id).orElse(null);
    }

    @Override
    public Inventory save(Inventory inventory) {
        return inventoryRepository.save(inventory);
    }

    @Override
    public void deleteById(Long id) {
        inventoryRepository.deleteById(id);
    }

    @Override
    @Transactional
    public void inventoryIn(InventoryOperationRequest request) {
        // 查找现有库存记录
        Inventory inventory = inventoryRepository.findByMedicineIdAndWarehouseId(
            request.getMedicineId(), request.getWarehouseId());

        int beforeStock = 0;
        if (inventory == null) {
            // 如果不存在，创建新的库存记录
            inventory = new Inventory();
            inventory.setMedicineId(request.getMedicineId());
            inventory.setWarehouseId(request.getWarehouseId());
            inventory.setQuantity(request.getQuantity());
        } else {
            // 如果存在，增加库存数量
            beforeStock = inventory.getQuantity();
            inventory.setQuantity(inventory.getQuantity() + request.getQuantity());
        }

        inventory.setLastUpdate(new Date());
        inventoryRepository.save(inventory);

        // 创建库存记录
        createInventoryRecord(request, "IN", beforeStock, inventory.getQuantity());
    }

    @Override
    @Transactional
    public void inventoryOut(InventoryOperationRequest request) {
        // 查找现有库存记录
        Inventory inventory = inventoryRepository.findByMedicineIdAndWarehouseId(
            request.getMedicineId(), request.getWarehouseId());

        if (inventory == null) {
            throw new RuntimeException("库存记录不存在");
        }

        if (inventory.getQuantity() < request.getQuantity()) {
            throw new RuntimeException("库存不足，当前库存: " + inventory.getQuantity());
        }

        int beforeStock = inventory.getQuantity();
        // 减少库存数量
        inventory.setQuantity(inventory.getQuantity() - request.getQuantity());
        inventory.setLastUpdate(new Date());
        inventoryRepository.save(inventory);

        // 创建库存记录
        createInventoryRecord(request, "OUT", beforeStock, inventory.getQuantity());
    }

    @Override
    public Page<InventoryRecord> getInventoryRecords(Long medicineId, String type, String operator,
                                                   Date startDate, Date endDate, Pageable pageable) {
        return inventoryRecordRepository.findRecordsWithFilters(
            medicineId, type, operator, startDate, endDate, pageable);
    }

    // 创建库存记录的辅助方法
    private void createInventoryRecord(InventoryOperationRequest request, String type,
                                     int beforeStock, int afterStock) {
        InventoryRecord record = new InventoryRecord();
        record.setMedicineId(request.getMedicineId());
        record.setMedicineName("阿莫西林胶囊"); // 临时硬编码，实际应该从数据库查询
        record.setType(type);
        record.setQuantity(request.getQuantity());
        record.setBeforeStock(beforeStock);
        record.setAfterStock(afterStock);
        record.setReason(request.getReason());
        record.setOperatorId(1L); // 临时硬编码，实际应该从当前用户获取
        record.setOperatorName("系统管理员"); // 临时硬编码，实际应该从当前用户获取

        inventoryRecordRepository.save(record);
    }
}