<template>
  <div class="inventory-alert">
    <!-- 预警统计卡片 -->
    <div class="alert-stats">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="stat-card urgent">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ urgentCount }}</div>
                <div class="stat-label">紧急预警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ warningCount }}</div>
                <div class="stat-label">库存不足</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="stat-card normal">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ normalCount }}</div>
                <div class="stat-label">库存正常</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 预警列表 -->
    <el-card class="alert-list">
      <template #header>
        <div class="card-header">
          <span>库存预警列表</span>
          <el-button type="primary" size="small" @click="refreshAlerts">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="alertList"
        stripe
        style="width: 100%"
      >
        <el-table-column label="预警级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getAlertTagType(row.level)" :effect="row.level === 'urgent' ? 'dark' : 'plain'">
              {{ getAlertLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="medicineName" label="药品名称" min-width="150" />
        
        <el-table-column prop="warehouseName" label="仓库" width="100" />
        
        <el-table-column label="当前库存" width="100">
          <template #default="{ row }">
            <span :class="getStockClass(row.level)">{{ row.currentStock }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="minStock" label="最低库存" width="100" />
        
        <el-table-column label="缺口" width="80">
          <template #default="{ row }">
            <span class="stock-gap">{{ Math.max(0, row.minStock - row.currentStock) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastUpdate" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.lastUpdate) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleQuickRestock(row)">
              快速补货
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 快速补货对话框 -->
    <el-dialog
      v-model="restockVisible"
      title="快速补货"
      width="40%"
      destroy-on-close
    >
      <div v-if="selectedAlert" class="restock-form">
        <div class="alert-info">
          <h4>{{ selectedAlert.medicineName }}</h4>
          <p>当前库存: <span class="current-stock">{{ selectedAlert.currentStock }}</span></p>
          <p>最低库存: <span class="min-stock">{{ selectedAlert.minStock }}</span></p>
          <p>建议补货: <span class="suggest-stock">{{ suggestedQuantity }}</span></p>
        </div>
        
        <el-form :model="restockForm" label-width="80px">
          <el-form-item label="补货数量">
            <el-input-number
              v-model="restockForm.quantity"
              :min="1"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="补货原因">
            <el-input
              v-model="restockForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入补货原因..."
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="restockVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmRestock">确认补货</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Warning, InfoFilled, CircleCheckFilled, Refresh } from '@element-plus/icons-vue';
import { getLowStockInventory } from '@/api/modules/inventory';

// 响应式数据
const loading = ref(false);
const alertList = ref<any[]>([]);
const restockVisible = ref(false);
const selectedAlert = ref<any>(null);

// 统计数据
const urgentCount = computed(() => alertList.value.filter(item => item.level === 'urgent').length);
const warningCount = computed(() => alertList.value.filter(item => item.level === 'warning').length);
const normalCount = computed(() => alertList.value.filter(item => item.level === 'normal').length);

// 建议补货数量
const suggestedQuantity = computed(() => {
  if (!selectedAlert.value) return 0;
  const gap = selectedAlert.value.minStock - selectedAlert.value.currentStock;
  return Math.max(gap, selectedAlert.value.minStock);
});

// 补货表单
const restockForm = reactive({
  quantity: 0,
  reason: '库存预警自动补货'
});

// 获取预警级别标签类型
const getAlertTagType = (level: string) => {
  switch (level) {
    case 'urgent': return 'danger';
    case 'warning': return 'warning';
    default: return 'success';
  }
};

// 获取预警级别文本
const getAlertLevelText = (level: string) => {
  switch (level) {
    case 'urgent': return '紧急';
    case 'warning': return '不足';
    default: return '正常';
  }
};

// 获取库存样式类
const getStockClass = (level: string) => {
  switch (level) {
    case 'urgent': return 'stock-urgent';
    case 'warning': return 'stock-warning';
    default: return 'stock-normal';
  }
};

// 时间格式化
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 获取预警列表
const fetchAlerts = async () => {
  loading.value = true;
  try {
    const result = await getLowStockInventory();
    alertList.value = (result || []).map((item: any) => ({
      ...item,
      level: item.currentStock <= 10 ? 'urgent' : 'warning'
    }));
  } catch (error: any) {
    console.error('获取预警列表失败:', error);
    ElMessage.error('获取预警列表失败');
  } finally {
    loading.value = false;
  }
};

// 刷新预警
const refreshAlerts = () => {
  fetchAlerts();
  ElMessage.success('预警列表已刷新');
};

// 处理快速补货
const handleQuickRestock = (row: any) => {
  selectedAlert.value = row;
  restockForm.quantity = suggestedQuantity.value;
  restockVisible.value = true;
};

// 确认补货
const handleConfirmRestock = () => {
  ElMessage.success('补货申请已提交');
  restockVisible.value = false;
  fetchAlerts(); // 刷新列表
};

// 页面加载时获取数据
onMounted(() => {
  fetchAlerts();
});
</script>

<style scoped>
.inventory-alert {
  padding: 20px;
}

.alert-stats {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.urgent {
  border-left: 4px solid #f56c6c;
}

.stat-card.warning {
  border-left: 4px solid #e6a23c;
}

.stat-card.normal {
  border-left: 4px solid #67c23a;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
}

.stat-card.urgent .stat-icon {
  color: #f56c6c;
}

.stat-card.warning .stat-icon {
  color: #e6a23c;
}

.stat-card.normal .stat-icon {
  color: #67c23a;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.alert-list {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-urgent {
  color: #f56c6c;
  font-weight: 600;
}

.stock-warning {
  color: #e6a23c;
  font-weight: 600;
}

.stock-normal {
  color: #67c23a;
  font-weight: 600;
}

.stock-gap {
  color: #f56c6c;
  font-weight: 600;
}

.restock-form {
  padding: 16px 0;
}

.alert-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.alert-info h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.alert-info p {
  margin: 8px 0;
  color: #7f8c8d;
}

.current-stock {
  color: #f56c6c;
  font-weight: 600;
}

.min-stock {
  color: #e6a23c;
  font-weight: 600;
}

.suggest-stock {
  color: #67c23a;
  font-weight: 600;
}
</style>
