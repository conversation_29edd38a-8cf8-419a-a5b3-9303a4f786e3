package com.example.medicine.repository;

import com.example.medicine.entity.InventoryRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface InventoryRecordRepository extends JpaRepository<InventoryRecord, Long> {
    
    // 根据药品ID查找记录
    Page<InventoryRecord> findByMedicineIdOrderByCreateTimeDesc(Long medicineId, Pageable pageable);
    
    // 根据药品ID和操作类型查找记录
    Page<InventoryRecord> findByMedicineIdAndTypeOrderByCreateTimeDesc(Long medicineId, String type, Pageable pageable);
    
    // 复杂查询：根据多个条件查找记录
    @Query("SELECT ir FROM InventoryRecord ir WHERE " +
           "(:medicineId IS NULL OR ir.medicineId = :medicineId) AND " +
           "(:type IS NULL OR :type = '' OR ir.type = :type) AND " +
           "(:operator IS NULL OR :operator = '' OR ir.operatorName LIKE %:operator%) AND " +
           "(:startDate IS NULL OR ir.createTime >= :startDate) AND " +
           "(:endDate IS NULL OR ir.createTime <= :endDate) " +
           "ORDER BY ir.createTime DESC")
    Page<InventoryRecord> findRecordsWithFilters(
        @Param("medicineId") Long medicineId,
        @Param("type") String type,
        @Param("operator") String operator,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate,
        Pageable pageable
    );
}
